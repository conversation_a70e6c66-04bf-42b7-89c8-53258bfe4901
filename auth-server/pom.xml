<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.zjwn.base</groupId>
        <artifactId>base-platform</artifactId>
        <version>springboot3-1.2.10</version>
    </parent>
    <artifactId>auth-server</artifactId>
    <name>auth-server</name>
    <description>pc通用web服务</description>


    <dependencies>
        <dependency>
            <groupId>com.zjwn.base</groupId>
            <artifactId>auth-server-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-webflux</artifactId>
        </dependency>
    </dependencies>

    <distributionManagement>
        <repository>
            <id>maven-releases</id>
            <name>Releases Repository</name>
            <url>http://*************:8081/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>maven-snapshots</id>
            <name>Snapshot Repository</name>
            <url>http://*************:8081/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>
</project>
