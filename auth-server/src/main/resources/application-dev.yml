server:
    port: 7188
spring:
    datasource:
        dynamic:
            datasource:
                master:
                    url: *****************************************************************
                    username: <PERSON><PERSON>(LCJFWPXdBU7REMShT+aY71DisS8LFW98)
                    password: <PERSON><PERSON>(37bcm9lA2jkQlJQiV1pnBUmgl10XJlNm)
                    driver-class-name: com.p6spy.engine.spy.P6SpyDriver
    data:
        redis:
            host: *************
            port: 6379
            password: <PERSON><PERSON>(ay3eMpgZVkg9O4/pGepmUDPiekRzW8+z)
            database: 0

sa-token:
    # token有效期，单位s 默认30天, -1代表永不过期
    timeout: 86400
    # token临时有效期 (指定时间内无操作就视为token过期) 单位: 秒
    active-timeout: 3600

jetcache:
    remote:
        default:
            uri:
                - redis://${spring.data.redis.password}@${spring.data.redis.host}:${spring.data.redis.port}/${spring.data.redis.database}

