package com.zjhh.aialys.config;

import com.zjhh.aialys.constant.PromptConstant;
import com.zjhh.aialys.dao.entity.ChatAlysLlmSet;
import com.zjhh.aialys.dao.mapper.ChatAlysLlmSetMapper;
import jakarta.annotation.Resource;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.client.advisor.SimpleLoggerAdvisor;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @since 2025/6/3 15:21
 */
@Component
public class AiAlysChatComponent {

    @Resource
    private ChatAlysLlmSetMapper chatAlysLlmSetMapper;

    private final ConcurrentHashMap<String, ChatModel> OPENAI_CHAT_MODELS = new ConcurrentHashMap<>();

    private final ConcurrentHashMap<String, ChatClient> OPENAI_CHAT_CLIENT = new ConcurrentHashMap<>();

    public ChatClient getChatClient(String llmId) {
        ChatClient chatClient = OPENAI_CHAT_CLIENT.get(llmId);
        if (chatClient == null) {
            ChatModel chatModel = getChatModel(llmId);
            chatClient = ChatClient.builder(chatModel)
                    .defaultSystem(PromptConstant.DEFAULT_SYSTEM_PROMPT)
                    // 实现 Logger 的 Advisor
                    .defaultAdvisors(
                            new SimpleLoggerAdvisor()
                    )
                    // 设置 ChatClient 中 ChatModel 的 Options 参数
                    .defaultOptions(
                            OpenAiChatOptions.builder()
                                    .topP(0.7)
                                    .stream(true)  // 启用流式响应
                                    .build()
                    )
                    .build();
            OPENAI_CHAT_CLIENT.put(llmId, chatClient);
        }
        return chatClient;
    }

    /**
     * 获取对话模型
     *
     * @param llmId
     * @return
     */
    public ChatModel getChatModel(String llmId) {
        ChatModel chatModel = OPENAI_CHAT_MODELS.get(llmId);
        if (chatModel == null) {
            ChatAlysLlmSet chatAlysLlmSet = chatAlysLlmSetMapper.selectById(llmId);
            chatModel = OpenAiChatModel.builder()
                    .openAiApi(OpenAiApi
                            .builder()
                            .baseUrl(chatAlysLlmSet.getBaseUrl())
                            .apiKey(chatAlysLlmSet.getApiKey())
                            .completionsPath(chatAlysLlmSet.getCompletionsPath())
                            .build())
                    .defaultOptions(OpenAiChatOptions
                            .builder()
                            .model(chatAlysLlmSet.getModelId())
                            .stream(true)  // 启用流式响应
                            .build()
                    )
                    .build();
            OPENAI_CHAT_MODELS.put(llmId, chatModel);
        }
        return chatModel;
    }

    /**
     * 清空缓存中的对话模型
     *
     * @param llmId
     */
    public void clear(String llmId) {
        OPENAI_CHAT_CLIENT.remove(llmId);
        OPENAI_CHAT_MODELS.remove(llmId);
    }
}
