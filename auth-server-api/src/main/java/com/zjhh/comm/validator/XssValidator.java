package com.zjhh.comm.validator;

import cn.hutool.core.util.StrUtil;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.safety.Safelist;

/**
 * <AUTHOR>
 * @since 2024/9/14 上午9:08
 */
public class XssValidator implements ConstraintValidator<Xss, String> {

    private static final Safelist WHITE_LIST = Safelist.relaxed();

    /**
     * 定义输出设置，关闭prettyPrint（prettyPrint=false），目的是避免在清理过程中对代码进行格式化
     * 从而保持输入和输出内容的一致性。
     */
    private static final Document.OutputSettings OUTPUT_SETTINGS = new Document.OutputSettings().prettyPrint(false);

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {

        if (StrUtil.isBlank(value)) {
            return true;
        }

        // 检查是否为URL格式，如果是URL则使用不同的验证逻辑
        if (isUrl(value)) {
            return validateUrl(value);
        }

        // 使用Jsoup库对输入值进行清理，以移除潜在的XSS攻击脚本。
        // 使用预定义的白名单和输出设置来确保只保留安全的HTML元素和属性。
        String cleanedValue = Jsoup.clean(value, "", WHITE_LIST, OUTPUT_SETTINGS);

        // 比较清理后的值与原始值是否相同，用于判断输入值是否有效。
        return cleanedValue.equals(value);
    }

    /**
     * 检查字符串是否为URL格式
     */
    private boolean isUrl(String value) {
        return value.matches("^https?://.*");
    }

    /**
     * 验证URL是否安全（不包含XSS攻击脚本）
     */
    private boolean validateUrl(String url) {
        // 检查是否包含危险的协议
        String lowerUrl = url.toLowerCase();
        if (lowerUrl.startsWith("javascript:") || lowerUrl.startsWith("data:") ||
            lowerUrl.startsWith("vbscript:") || lowerUrl.contains("javascript:") ||
            lowerUrl.contains("vbscript:")) {
            return false;
        }

        // 检查是否包含HTML标签
        if (url.contains("<") || url.contains(">")) {
            return false;
        }

        // 检查是否包含其他危险字符或脚本
        if (url.contains("script") || url.contains("onload") || url.contains("onerror") ||
            url.contains("onclick") || url.contains("onmouseover")) {
            return false;
        }

        // 对于正常的HTTP/HTTPS URL，允许通过
        return true;
    }

}
