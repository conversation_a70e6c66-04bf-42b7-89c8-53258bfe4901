package com.zjhh;

import com.baomidou.dynamic.datasource.toolkit.CryptoUtils;
import com.zjhh.comm.exception.BizException;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2023/4/23 09:46
 */
@Slf4j
public class Test {

    private static String getName(String filename) {
        try {
            String[] names = filename.split("_");
            return names[2].substring(0, 8);
        } catch (Exception e) {
            throw new BizException("文件名不符合要求！");
        }
    }

    public static void main(String[] args) throws Exception {
        System.out.println(getName("6114_1105140000_20250919.xml"));
    }
}
