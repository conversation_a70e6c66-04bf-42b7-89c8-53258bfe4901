package com.zjhh.web;

import com.zjhh.comm.validator.XssValidator;
import com.zjhh.user.dao.entity.Menu;
import com.zjhh.user.dao.mapper.MenuMapper;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.safety.Safelist;

import java.util.List;

@SpringBootTest
class WebBaseApplicationTests {

    @Resource
    private MenuMapper menuMapper;

    @Test
    void contextLoads() {
        List<Menu> list = menuMapper.selectList(null);
        list.forEach(menu -> {
            Menu newMenu = new Menu();
            newMenu.setId(menu.getId());
            newMenu.setPath(menu.getRouter().toLowerCase() + System.currentTimeMillis());
            menuMapper.updateById(newMenu);
        });
    }

    @Test
    void testXssValidator() {
        XssValidator validator = new XssValidator();

        // 测试你的URL
        String url1 = "http://ys.zjhhsoft.com/swys/#id=nxj92p&p=%E5%AF%BC%E8%88%AA&sc=2&c=1";
        String url2 = "http://ys.zjhhsoft.com/swys/#id=nxj92p&p=导航&sc=2&c=1";

        System.out.println("原始URL1: " + url1);
        System.out.println("验证结果1: " + validator.isValid(url1, null));

        System.out.println("原始URL2: " + url2);
        System.out.println("验证结果2: " + validator.isValid(url2, null));

        // 查看Jsoup清理后的结果
        Safelist whiteList = Safelist.relaxed();
        Document.OutputSettings outputSettings = new Document.OutputSettings().prettyPrint(false);

        String cleaned1 = Jsoup.clean(url1, "", whiteList, outputSettings);
        String cleaned2 = Jsoup.clean(url2, "", whiteList, outputSettings);

        System.out.println("清理后URL1: " + cleaned1);
        System.out.println("清理后URL2: " + cleaned2);

        System.out.println("URL1相等: " + cleaned1.equals(url1));
        System.out.println("URL2相等: " + cleaned2.equals(url2));
    }

}
